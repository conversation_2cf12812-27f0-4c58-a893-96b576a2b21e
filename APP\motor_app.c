#include "motor_app.h"
#include <math.h>
#include <stdint.h>


Motor_t motor1;
Motor_t motor2;

void Motor_Init(void)
{

	TB6612_Init(1);
	//假设电机1正装 电机2为反装
	Motor_Create(&motor1,&htim2,TIM_CHANNEL_1,GPIOA,GPIO_PIN_2,GPIOB,GPIO_PIN_3,1);
	Motor_Create(&motor2,&htim2,TIM_CHANNEL_2,GPIOB,GPIO_PIN_12,GPIOB,GPIO_PIN_13,0);
}


void motor_task(void)
{
	Motor_SetSpeed(&motor1, 400);
	HAL_Delay(1000);
}

//#include "math.h"

//#define SINE_TABLE_SIZE 100  // 正弦表点数 ———— 更新次数
//#define PWM_PERIOD      99  // ARR值

//uint16_t sineWave[SINE_TABLE_SIZE];  // 正弦波占空比表
//uint8_t waveIndex = 0;               // 当前索引


//void sine_init()
//{
//	// 生成正弦波表 (范围: 0~PWM_PERIOD)
//	for(int i=0; i < SINE_TABLE_SIZE; i++)
//	{
//		float radian = 2 * 3.14159f * i / SINE_TABLE_SIZE;
//		// 正弦波转换到0-1范围，再映射到PWM范围
//		sineWave[i] = (uint16_t)(1000 * (0.5f + 0.5f * sinf(radian)));
//	}

//	// 启动PWM和中断
//	HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_2);
//	HAL_TIM_Base_Start_IT(&htim3);  // 开启定时器中断
//}


///**
// * @brief 定时器中断服务函数
// * @param htim 定时器句柄
// */
//void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim) {
//    if(htim->Instance == TIM3) {
//        // 移动正弦表索引（循环）
//        waveIndex = (waveIndex + 1) % SINE_TABLE_SIZE;
//        // 更新转速 (查表)
//        Motor_SetSpeed(&motor1,sineWave[waveIndex]);
//        Motor_SetSpeed(&motor2,sineWave[waveIndex]);
//    }
//}

