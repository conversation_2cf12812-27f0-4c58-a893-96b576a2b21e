#ifndef MYDEFINE_H
#define MYDEFINE_H

#include "main.h"
#include "tim.h"
#include "gpio.h"

#include "stdio.h"
#include "stdlib.h"
#include "stdarg.h"
#include "string.h"

#include "motor.h"
#include "ringbuffer.h"

#include "scheduler.h"
#include "motor_app.h"
#include "usart_app.h"

extern TIM_HandleTypeDef htim2;
extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;

extern uint8_t uart_rx_dma_buffer[128];
extern struct rt_ringbuffer uart_ringbuffer;
extern uint8_t ringbuffer_pool[128];

#endif



